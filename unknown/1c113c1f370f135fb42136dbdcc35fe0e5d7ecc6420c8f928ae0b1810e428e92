<?php
// Disable error display to prevent HTML output
ini_set('display_errors', 0);
error_reporting(0);



// Set headers first
header('Content-Type: application/json');

try {
    require_once 'dbconnection.php';
    
    // Create database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Validate level parameter first
    if (!isset($_GET['level']) || !is_numeric($_GET['level'])) {
        throw new Exception("Invalid or missing level parameter");
    }

    $Clevel = $_GET['level'];

    // Prepare and execute the query with parameter binding
    $stmt = $conn->prepare("SELECT *
                           FROM game_content
                           WHERE level_number = ?
                           ORDER BY RAND()");
    $stmt->execute([$Clevel]);
    
    // Fetch all results
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Return the results as JSON
    echo json_encode([
        'success' => true,
        'data' => $results
    ]);
    
} catch(PDOException $e) {
    // Return error message as JSON
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} catch(Exception $e) {
    // Catch any other errors
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
