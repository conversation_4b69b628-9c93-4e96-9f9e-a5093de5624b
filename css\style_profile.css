/* Base Styles */
:root {
    --bg-dark: rgb(11, 8, 16);
    --bg-darker: rgb(5, 3, 10);
    --bg-light: rgb(30, 25, 40);
    --primary: #8a63f2;
    --primary-dark: #6a4bc7;
    --secondary: #ff9e3f;
    --success: #6fcf97;
    --warning: #f2c94c;
    --danger: #eb5757;
    --light: rgba(255, 255, 255, 0.9);
    --dark: rgba(0, 0, 0, 0.8);
    --gray: rgba(255, 255, 255, 0.1);
    --dark-gray: rgba(255, 255, 255, 0.4);
    --text: rgba(255, 255, 255, 0.9);
    --text-secondary: rgba(255, 255, 255, 0.7);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Cinzel", "sans-serif";
}

body {
    background-color: var(--bg-dark);
    color: var(--text);
    line-height: 1.6;
    min-height: 100vh;
    padding: 2rem;
    animation: visibility .5s 1;
}

@keyframes visibility{
    0%{
        opacity: 0;
    }
    100%{
        opacity: 1;
    }
}

/* Game Container */
.game-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem;
    background: var(--bg-light);
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.game-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--gray);
}

.game-header h1 {
    color: white;
    margin-bottom: 1rem;
    font-size: 2.5rem;
    font-family: "Cinzel", "sans-serif";
    letter-spacing: 1px;
}

/* XP Bar */
.xp-bar {
    height: 30px;
    background-color: var(--bg-darker);
    border-radius: 15px;
    position: relative;
    overflow: hidden;
    margin: 0 auto;
    max-width: 500px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.5);
}

.xp-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--secondary));
    border-radius: 15px;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(138, 99, 242, 0.3);
}

.xp-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

/* Profile Editor Layout */
.profile-editor {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
}

@media (max-width: 768px) {
    .profile-editor {
        grid-template-columns: 1fr;
    }
}

/* Avatar Section */
.avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.avatar-container {
    position: relative;
    margin-bottom: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.avatar {
    width: 160px;
    height: 160px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    border: 5px solid var(--primary);
    box-shadow: 0 5px 20px rgba(138, 99, 242, 0.3);
    transition: transform 0.3s ease;
}

.avatar:hover {
    transform: scale(1.05);
}

.avatar-controls {
    margin-top: 1.5rem;
    text-align: center;
    display: flex;
    gap: 10px;
}

/* Form Section */
.completion-meter {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    gap: 0.8rem;
    font-size: 0.95rem;
    color: var(--text-secondary);
}

.meter {
    flex-grow: 1;
    height: 10px;
    background-color: var(--bg-darker);
    border-radius: 5px;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.meter-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--secondary));
    transition: width 0.5s ease;
    box-shadow: 0 0 8px rgba(138, 99, 242, 0.3);
}

.profile-form {
    display: flex;
    flex-direction: column;
    gap: 1.8rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.6rem;
}

.form-group label {
    font-weight: 600;
    color: white;
    font-size: 0.95rem;
    letter-spacing: 0.5px;
    font-family: "Cinzel", "sans-serif";
}

.input-with-badge {
    position: relative;
}

.input-with-badge input,
.input-with-badge textarea {
    width: 100%;
    padding: 0.9rem 1rem;
    background-color: var(--bg-darker);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    color: var(--text);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

.input-with-badge textarea {
    min-height: 120px;
    resize: vertical;
}

.input-with-badge input:focus,
.input-with-badge textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(138, 99, 242, 0.2),
                inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

.input-badge {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 22px;
    height: 22px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

.input-badge.complete {
    background-color: var(--success);
    color: var(--dark);
}

.input-badge.incomplete {
    background-color: var(--warning);
    color: var(--dark);
}

/* Buttons */
.btn-edit,
.btn-delete,
.btn-save,
.btn-cancel {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.95rem;
    letter-spacing: 0.5px;
    font-family: "Cinzel", "sans-serif";
}

.btn-edit, .btn-delete {
    background-color: var(--primary);
    color: white;
    box-shadow: 0 4px 15px rgba(138, 99, 242, 0.3);
}

.btn-edit:hover, .btn-delete:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(138, 99, 242, 0.4);
}

.btn-save {
    background-color: var(--success);
    color: var(--dark);
    box-shadow: 0 4px 15px rgba(111, 207, 151, 0.3);
}

.btn-save:hover {
    background-color: #5dbb81;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(111, 207, 151, 0.4);
}

.btn-cancel {
    background-color: var(--bg-darker);
    color: var(--text);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-cancel:hover {
    background-color: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}


/* Glow effects for interactive elements */
.btn-edit, .btn-save, .badge:not(.locked) {
    position: relative;
    overflow: hidden;
}

.btn-edit::after, .btn-save::after, .badge:not(.locked)::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        to bottom right,
        rgba(255, 255, 255, 0) 45%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0) 55%
    );
    transform: rotate(30deg);
    animation: shine 3s infinite;
    opacity: 0;
}

/* Modal Overlay Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(3px);
}

/* Confirmation Modal Styles */
.confirmation-modal {
    position: relative;
    background-color: #2c3e50;
    padding: 25px;
    border-radius: 10px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    color: white;
    text-align: center;
    animation: modalAppear 0.3s ease-out;
}

@keyframes modalAppear {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.confirmation-modal h3 {
    margin-top: 0;
    color: white;
    font-size: 1.5rem;
}

.confirmation-modal p {
    margin-bottom: 25px;
    font-size: 1.1rem;
}

.confirmation-modal .modal-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

.confirmation-modal .btn-confirm,
.confirmation-modal .btn-cancel {
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s;
    border: none;
    outline: none;
}

.confirmation-modal .btn-confirm {
    background-color: #27ae60;
    color: white;
}

.confirmation-modal .btn-cancel {
    background-color: #e74c3c;
    color: white;
}

.confirmation-modal .btn-confirm:hover,
.confirmation-modal .btn-confirm:focus {
    background-color: #2ecc71;
    transform: translateY(-2px);
}

.confirmation-modal .btn-cancel:hover,
.confirmation-modal .btn-cancel:focus {
    background-color: #c0392b;
    transform: translateY(-2px);
}

/* Disable interactions with background elements */
.modal-open {
    overflow: hidden;
    pointer-events: none;
}

.modal-open .game-container {
    pointer-events: none;
    opacity: 0.5;
    filter: blur(1px);
}

.modal-open .confirmation-modal {
    pointer-events: auto;
}

/* Prevent text selection when modal is open */
.modal-open * {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Ensure modal content remains interactive */
.modal-open .confirmation-modal * {
    pointer-events: auto;
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
}

@keyframes shine {
    0% {
        transform: rotate(30deg) translate(-30%, -30%);
        opacity: 0;
    }
    20% {
        opacity: 0.5;
    }
    100% {
        transform: rotate(30deg) translate(30%, 30%);
        opacity: 0;
    }
}

/* Avatar Modal Styles */
.avatar-modal {
    position: relative;
    background-color: #2c3e50;
    padding: 25px;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    color: white;
    text-align: center;
    animation: modalAppear 0.3s ease-out;
    
}

.avatar-modal h3 {
    margin-top: 0;
    color: #f1c40f;
    font-size: 1.5rem;
    margin-bottom: 20px;
}

.avatar-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.avatar-option {
    cursor: pointer;
    transition: all 0.2s;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid transparent;
}

.avatar-option:hover {
    transform: scale(1.05);
    border-color: #f1c40f;
}

.avatar-option img {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 50%;
}

.avatar-modal .modal-actions {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

/* Reuse existing modal overlay styles from previous implementation */

/* Upload Section Styles */
.upload-section {
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    text-align: center;
}

.btn-upload {
    display: inline-block;
    padding: 10px 15px;
    background-color: rgb(8, 39, 197);
    color: white;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
    margin-bottom: 10px;
}

.btn-upload:hover {
    background-color: rgb(2, 29, 164);
}

#upload-preview {
    margin-top: 15px;
}

#upload-preview .avatar-option {
    position: relative;
    cursor: pointer;
}

#upload-preview .avatar-option p {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px;
    margin: 0;
    text-align: center;
    border-bottom-left-radius: 50%;
    border-bottom-right-radius: 50%;
}
/* Avatar Modal Specific Styles */
.avatar-upload-input {
    display: none;
}

.avatar-upload-btn {
    display: inline-block;
    padding: 10px 15px;
    background-color: #3498db;
    color: white;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
    margin-bottom: 10px;
}

.avatar-upload-btn:hover {
    background-color: #2980b9;
}

.avatar-upload-preview {
    display: none;
    margin-top: 15px;
}

.avatar-modal-cancel-btn {
    background-color: #e74c3c;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
}

.avatar-modal-cancel-btn:hover {
    background-color: #c0392b;
}

/* Confirmation Modal Specific Styles */
.avatar-confirmation-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1001;
}

.avatar-confirmation-modal {
    background-color: #2c3e50;
    padding: 25px;
    border-radius: 10px;
    min-width: 20vw;
    max-width: 40vw;
    text-align: center;
}

.avatar-confirmation-title {
    margin-top: 0;
    color: #f1c40f;
}

.avatar-confirmation-preview {
    margin: 20px 0;
}

.avatar-confirmation-img {
    min-height: 45vh;
    min-width: 20vh;
    margin: 0 auto;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    border: 3px solid white;
}

.avatar-confirmation-text {
    margin-bottom: 20px;
}

.avatar-confirmation-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.avatar-confirm-btn {
    background-color: #27ae60;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
}

.avatar-confirm-btn:hover {
    background-color: #2ecc71;
}

.avatar-cancel-btn {
    background-color: #e74c3c;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
}

.avatar-cancel-btn:hover {
    background-color: #c0392b;
}

.modal-overlay {
    pointer-events: none; /* Disabled by default */
}

.avatar-modal {
    pointer-events: auto; /* Re-enable for modal content */
}

/* For confirmation modal */
.avatar-confirmation-overlay {
    pointer-events: none;
}

.avatar-confirmation-modal {
    pointer-events: auto;
}