:root {
    --primary: #8a63f2;
    --primary-dark: #6a4bc7;
    --secondary: #ff9e3f;
    --success: #6fcf97;
    --warning: #f2c94c;
    --danger: #eb5757;
    --light: rgba(255, 255, 255, 0.9);
    --dark: rgba(0, 0, 0, 0.8);
    --gray: rgba(255, 255, 255, 0.1);
    --dark-gray: rgba(255, 255, 255, 0.4);
    --text: rgba(255, 255, 255, 0.9);
    --text-secondary: rgba(255, 255, 255, 0.7);
    --primary-color: rgb(15, 0, 227);
    --secondary-color: #a29bfe;
    --dark-color: #2d3436;
    --light-color: #f5f6fa;
    --text-light: #636e72;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

@keyframes visibility{
    0%{
        opacity: 0;

    }
    100%{
        opacity: 1;

    }
    
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Se<PERSON>e <PERSON>', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: rgb(11, 8, 16);
    color: var(--text);
    line-height: 1.6;
    min-height: 100vh;
    padding: 2rem;
    animation: visibility .5s 1;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: rgb(30, 25, 40);
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.05);
    overflow: hidden;
    padding: 2rem;
}

.header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray);
    text-align: center;
}

.header h1 {
    font-size: 2.5rem;
    color: white;
    font-family: "Cinzel", "sans-serif";
    letter-spacing: 1px;
}

.progress-section {
    padding: 2rem;
    border-bottom: 1px solid var(--gray);
}

.progress-section h2 {
    margin-bottom: 1rem;
    color: var(--text);
}

.progress-bar {
    height: 20px;
    background: var(--dark-gray);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--secondary));
    border-radius: 10px;
    transition: width 0.3s ease;
    width: 0;
}

.progress-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.achievements-section {
    padding: 2rem;
}

.achievements-count {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.achievements-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

.achievement-card {
    background: var(--dark);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    transition: transform 0.3s ease;
}

.achievement-card:hover {
    transform: translateY(-5px);
}

.achievement-card img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    margin-bottom: 1rem;
}

.achievement-card h3 {
    color: var(--text);
    margin-bottom: 0.5rem;
}

.achievement-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.unlocked-date {
    color: var(--success);
    font-size: 0.8rem;
}

@media (max-width: 768px) {
    body {
        padding: 1rem;
    }

    .container {
        border-radius: 8px;
    }

    .progress-section,
    .achievements-section {
        padding: 1rem;
    }

    .achievements-container {
        grid-template-columns: 1fr;
    }
}