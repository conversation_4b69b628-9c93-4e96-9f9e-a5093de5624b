<?php
session_start();
header('Content-Type: application/json');

require_once 'dbconnection.php';

// Check if user is logged in
if (!isset($_SESSION['userId'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

try {
    $database = new Database();
    $pdo = $database->getConnection();

    $user_id = $_SESSION['userId'];

    // Get total number of levels from game_content
    $stmt = $pdo->prepare("SELECT COUNT(DISTINCT level_number) as total_levels FROM game_content");
    $stmt->execute();
    $total_levels = $stmt->fetch(PDO::FETCH_ASSOC)['total_levels'];

    // Get user's completed levels (levelStar > 0)
    $stmt = $pdo->prepare("SELECT COUNT(*) as completed_levels FROM user_levels WHERE user_id = ? AND levelStar > 0");
    $stmt->execute([$user_id]);
    $completed_levels = $stmt->fetch(PDO::FETCH_ASSOC)['completed_levels'];

    // Get total number of tiers
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_tiers FROM exp");
    $stmt->execute();
    $total_tiers = $stmt->fetch(PDO::FETCH_ASSOC)['total_tiers'];

    // Get user's current tier and exp
    $stmt = $pdo->prepare("SELECT expID, userExp FROM user_exp WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $user_tier = $stmt->fetch(PDO::FETCH_ASSOC);

    // Get unlocked achievements
    $stmt = $pdo->prepare("
        SELECT a.*, ua.unlocked_at 
        FROM achievements a 
        JOIN user_achievements ua ON a.id = ua.achievement_id 
        WHERE ua.user_id = ? AND ua.unlocked = 1
    ");
    $stmt->execute([$user_id]);
    $unlocked_achievements = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Calculate level progress percentage
    $level_progress = $total_levels > 0 ? ($completed_levels / $total_levels) * 100 : 0;

    // Calculate tier progress percentage
    $tier_progress = $total_tiers > 0 ? ($user_tier['expID'] / $total_tiers) * 100 : 0;

    echo json_encode([
        'success' => true,
        'data' => [
            'levels' => [
                'completed' => $completed_levels,
                'total' => $total_levels,
                'progress' => $level_progress
            ],
            'tier' => [
                'current' => $user_tier['tierID'],
                'total' => $total_tiers,
                'progress' => $tier_progress,
                'exp' => $user_tier['userExp']
            ],
            'achievements' => [
                'unlocked' => $unlocked_achievements,
                'total' => count($unlocked_achievements)
            ]
        ]
    ]);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error',
        'error' => $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error',
        'error' => $e->getMessage()
    ]);
}
?>
