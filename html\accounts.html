<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Settings</title>
    <link rel="stylesheet" href="../css/style_accounts.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="game-container">
        <header class="game-header">
            <h1>⚙️ Account Settings ⚙️</h1>
        </header>

        <main class="accounts-editor">
            <section class="form-section">
                <div class="completion-meter">
                    <span>Account Security: </span>
                    <div class="meter">
                        <div class="meter-fill" style="width: 100%"></div>
                    </div>
                    <span>100%</span>
                </div>

                <form class="accounts-form" id="accounts-form" method="POST">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <div class="input-with-badge">
                            <input type="text" id="username" readonly>
                            <span class="input-badge complete">🔒</span>
                        </div>
                        <small class="field-note">Username cannot be changed</small>
                    </div>

                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <div class="input-with-badge">
                            <input type="email" id="email">
                            <span class="input-badge complete">✓</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="current-password">Current Password</label>
                        <div class="input-with-badge">
                            <input type="password" id="current-password" placeholder="Enter current password">
                            <span class="input-badge incomplete">!</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="new-password">New Password</label>
                        <div class="input-with-badge">
                            <input type="password" id="new-password" placeholder="Enter new password">
                            <span class="input-badge incomplete">!</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="confirm-password">Confirm New Password</label>
                        <div class="input-with-badge">
                            <input type="password" id="confirm-password" placeholder="Confirm new password">
                            <span class="input-badge incomplete">!</span>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn-save">💾 Save Changes</button>
                        <button type="button" class="btn-cancel" onclick="window.location.href='mainpage.html'">🚫 Cancel</button>
                    </div>
                </form>
            </section>
        </main>
    </div>

    <script src="../js/accounts.js"></script>
</body>
</html>
