// Function to handle form submission
document.getElementById('accounts-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Validate passwords
    const currentPassword = document.getElementById('current-password').value;
    const newPassword = document.getElementById('new-password').value;
    const confirmPassword = document.getElementById('confirm-password').value;
    
    // Check if passwords are being changed
    if (newPassword || confirmPassword) {
        if (!currentPassword) {
            alert('Please enter your current password to change it.');
            return;
        }
        
        if (newPassword !== confirmPassword) {
            alert('New passwords do not match.');
            return;
        }
        
        if (newPassword.length < 6) {
            alert('New password must be at least 6 characters long.');
            return;
        }
    }
    
    // Gather form data
    const formData = {
        email: document.getElementById('email').value,
        currentPassword: currentPassword,
        newPassword: newPassword
    };
    
    saveAccountChanges(formData);
});

function saveAccountChanges(formData) {
    fetch('../php/save_account.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
    })
    .then(handleResponse)
    .then(data => {
        if (data.success) {
            alert('Account settings saved successfully!');
            // Clear password fields
            document.getElementById('current-password').value = '';
            document.getElementById('new-password').value = '';
            document.getElementById('confirm-password').value = '';
            updateCompletionBadges();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving account settings: ' + error.message);
    });
}

function handleResponse(response) {
    if (!response.ok) {
        return response.text().then(text => {
            throw new Error(text || 'Network response was not ok');
        });
    }
    return response.json().catch(() => {
        throw new Error('Invalid JSON response');
    });
}

// Load account data
function loadAccountData() {
    fetch('../php/get_account.php')
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Populate form fields with fetched data
            document.getElementById('username').value = data.username || '';
            document.getElementById('email').value = data.email || '';
            
            // Update completion badges
            updateCompletionBadges();
        } else {
            console.error('Error loading account data:', data.message);
        }
    })
    .catch(error => {
        console.error('Error loading account data:', error);
    });
}

function updateCompletionBadges() {
    const email = document.getElementById('email').value;
    
    // Update email badge
    const emailBadge = document.querySelector('#email').parentElement.querySelector('.input-badge');
    if (email && email.trim() !== '') {
        emailBadge.className = 'input-badge complete';
        emailBadge.textContent = '✓';
    } else {
        emailBadge.className = 'input-badge incomplete';
        emailBadge.textContent = '!';
    }
    
    // Password fields are always incomplete unless being changed
    const passwordBadges = document.querySelectorAll('#current-password, #new-password, #confirm-password');
    passwordBadges.forEach(input => {
        const badge = input.parentElement.querySelector('.input-badge');
        if (input.value && input.value.trim() !== '') {
            badge.className = 'input-badge complete';
            badge.textContent = '✓';
        } else {
            badge.className = 'input-badge incomplete';
            badge.textContent = '!';
        }
    });
}

// Add event listeners for real-time badge updates
document.addEventListener('DOMContentLoaded', function() {
    loadAccountData();
    
    // Add input event listeners
    const inputs = document.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('input', updateCompletionBadges);
    });
});
